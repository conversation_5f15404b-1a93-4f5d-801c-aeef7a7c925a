/******************************************************************************
 * Copyright (c) 2024, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the
 *       names of its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 ******************************************************************************/

#pragma once

/**
 * Custom comparator that simply uses `operator <` of the given type.
 */
struct custom_less_op_t
{
  template <typename T>
  __host__ __device__ bool operator()(const T& lhs, const T& rhs)
  {
    return lhs < rhs;
  }
};

/**
 * Custom comparator that compares a tuple type's first element using `operator <`.
 */
struct compare_first_lt_op_t
{
  /**
   * We need to be able to have two different types for lhs and rhs, as the call to std::stable_sort with a
   * zip-iterator, will pass a thrust::tuple for lhs and a tuple_of_iterator_references for rhs.
   */
  template <typename LhsT, typename RhsT>
  __host__ __device__ bool operator()(const LhsT& lhs, const RhsT& rhs) const
  {
    return thrust::get<0>(lhs) < thrust::get<0>(rhs);
  }
};

/**
 * Function object to computes the modulo of a given value. Used within sort tests to reduce the value-range of sort
 * keys and, hence, cause more ties between sort keys.
 */
template <typename T>
struct mod_op_t
{
  T mod;
  __host__ __device__ T operator()(T val) const
  {
    return val % mod;
  }
};
