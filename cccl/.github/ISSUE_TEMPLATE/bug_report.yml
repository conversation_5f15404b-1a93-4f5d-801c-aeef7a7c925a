name: Bug Report
description: Create a report to help us improve
title: '[BUG]: '
labels: ['bug']
body:
  - type: checkboxes
    id: check-duplicates
    attributes:
      label: Is this a duplicate?
      description: Check for duplicate issues.
      options:
        - label: I confirmed there appear to be no [duplicate issues](https://github.com/NVIDIA/cccl/issues) for this bug and that I agree to the [Code of Conduct](CODE_OF_CONDUCT.md)
          required: true

  - type: dropdown
    id: bug-type
    attributes:
      label: Type of Bug
      description: What kind of bug are you running into?
      multiple: false
      options:
        - Silent Failure
        - Runtime Error
        - Compile-time Error
        - Performance
        - Something else
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which CCCL component does this apply to?
      multiple: false
      options:
        - Thrust
        - CUB
        - libcu++
        - CUDA Experimental (cudax)
        - cuda.cooperative (Python)
        - cuda.parallel (Python)
        - General CCCL
        - Infrastructure
        - Not sure
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what problem you are running into.
      placeholder: "Attempting to use structured bindings with `cuda::std::tuple` fails to compile."
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: How to Reproduce
      description: Steps used to reproduce the bug.
      placeholder: |
        0. See godbolt link below for exact reproducer
        1. Construct a `cuda::std::tuple`
        2. Use structured bindings to destructure the elements of the tuple.
        3. Compilation fails with the error message:
        ```
        <source>(5): error: cannot bind to non-public member "cuda::std::__4::tuple<_Tp...>::__base_ [with _Tp=<int, int>]"
        auto [a,b] = t;
        ```
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: "Using structured bindings with `cuda::std::tuple` should successfully compile and destructure the elements of the tuple."
    validations:
      required: true

  - type: input
    id: reproduction-link
    attributes:
      label: Reproduction link
      description: If applicable, please provide a Compiler Explorer (godbolt) link to help explain your problem.
      placeholder:  https://godbolt.org/z/dT5nMcf7W
    validations:
      required: false

  - type: markdown
    attributes:
      value: '# System information'

  - type: input
    id: operating-system
    attributes:
      label: Operating System
      description:
        If applicable, the OS version where this bug occurs.
      placeholder: Ubuntu Linux 20.04
    validations:
      required: false

  - type: textarea
    id: nvidia-smi-output
    attributes:
      label: nvidia-smi output
      description: If applicable, the output from running the `nvidia-smi` command.
      placeholder: |
        +-----------------------------------------------------------------------------+
        | NVIDIA-SMI 495.29.05    Driver Version: 495.29.05    CUDA Version: 11.5     |
        |-------------------------------+----------------------+----------------------+
        | GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |
        | Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |
        |                               |                      |               MIG M. |
        |===============================+======================+======================|
        |   0  NVIDIA GeForce ...  Off  | 00000000:41:00.0  On |                  N/A |
        |  0%   25C    P8     8W / 320W |    491MiB / 10015MiB |      0%      Default |
        |                               |                      |                  N/A |
        +-------------------------------+----------------------+----------------------+
    validations:
      required: false

  - type: textarea
    id: nvcc-version
    attributes:
      label: NVCC version
      description: If applicable, the version of nvcc you're using.
      placeholder: |
        nvcc --version
        nvcc: NVIDIA (R) Cuda compiler driver
        Copyright (c) 2005-2021 NVIDIA Corporation
        Built on Thu_Nov_18_09:45:30_PST_2021
        Cuda compilation tools, release 11.5, V11.5.119
        Build cuda_11.5.r11.5/compiler.30672275_0
    validations:
      required: false
