# SPDX-FileCopyrightText: Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Set PR and Linked Issues to In Review

on:
  pull_request_target:
    # Run this action when a PR is opened or edited
    # Issues do not have a graphQL connection to linked PRs so we can't use that event
    types: [ready_for_review]

env:
 ORG: ${{ github.event.repository.owner.login }}
 PR_NUMBER: ${{ github.event.pull_request.number }}
 REPO: ${{ github.event.repository.name }}

 # The environment vars below are hard-coded from external queries to save time + complexity here
 # Note: PVT means Project V2, not "Private"
 # PVT = Project V2, PVTSSF = Project V2 Single Select Field, PVTIF = Project V2 Iteration Field
 PROJECT_ID: "PVT_kwDOABpemM4AEhOI"
 STATUS_FIELD_ID: "PVTSSF_lADOABpemM4AEhOIzgCmnYc"
 IN_REVIEW_PROJECT_OPTION_ID: "c6b49c6b"

jobs:
  query_and_mutate_project_fields:
    runs-on: ubuntu-latest

    steps:
      - name: Generate token
        id: generate_token
        uses: tibdex/github-app-token@v1.8.0
        with:
          app_id: ${{ secrets.CCCL_AUTH_APP_ID }}
          private_key: ${{ secrets.CCCL_AUTH_APP_PEM }}

      - name: Wait 1 Second
        id: sleep
        run: sleep 1

      - name: Get PR Project ID
        id: get_pr_id
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        run: |
            # Query up to 10 projects for the PR
            gh api graphql -f query='
              query {
                organization(login: "${{ env.ORG }}") {
                  repository(name: "${{ env.REPO }}") {
                    issueOrPullRequest(number: ${{ env.PR_NUMBER }}) {
                      ... on PullRequest {
                        id
                        projectItems(first: 10) {
                          edges {
                            node {
                              id
                              project {
                                id
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }' > project_data.json

            # Filter the json result to only the project-specific ID for the PR
            # A PR can be in multiple projects so we need to filter by the project ID we want
            pr_id=$(jq -r '.data.organization.repository.issueOrPullRequest.projectItems.edges[] |
                           select(.node.project.id == "${{ env.PROJECT_ID }}") |
                           .node.id' project_data.json)
            echo "PR_ID=$pr_id" >> $GITHUB_ENV
        continue-on-error: true

      - name: Set PR to In Review
        id: set_pr_in_review
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        run: |
            # Update the PR status to In Review
            gh api graphql -f query='
              mutation {
              updateProjectV2ItemFieldValue(
                  input: {
                      projectId: "${{ env.PROJECT_ID }}"
                      itemId: "${{ env.PR_ID }}"
                      fieldId: "${{ env.STATUS_FIELD_ID }}"
                      value: {
                          singleSelectOptionId: "${{ env.IN_REVIEW_PROJECT_OPTION_ID }}"
                      }
                      }
                  ) {
                      projectV2Item {
                      id
                      }
                  }
                }'
        continue-on-error: true

      - name: Set Linked Issues to In Review
        id: update_linked_issues
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        run: |
            gh api graphql -f query='
              query {
                organization(login: "${{ env.ORG }}") {
                  repository(name: "${{ env.REPO }}") {
                    issueOrPullRequest(number: ${{ env.PR_NUMBER }}) {
                      ... on PullRequest {
                        id
                        closingIssuesReferences(first: 10) {
                          edges {
                            node {
                              id
                              projectItems(first: 10) {
                                nodes {
                                  id
                                }
                                edges {
                                  node {
                                    id
                                    project {
                                      id
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }' > linked_issue_data.json
            issue_ids=$(jq -r '.data.organization.repository.issueOrPullRequest.closingIssuesReferences.edges[].node.projectItems.edges[] |
                               select(.node.project.id == "${{ env.PROJECT_ID }}") |
                               .node.id' linked_issue_data.json)

            # Set Linked Issues to In Review
            for issue_id in $issue_ids; do
              # The query below is constructed differently than the others due to bash variable syntax + github actions syntax interactions
              QUERY="mutation {
                updateProjectV2ItemFieldValue(
                  input: {
                    projectId: \"$PROJECT_ID\"
                    itemId: \"$issue_id\"
                    fieldId: \"$STATUS_FIELD_ID\"
                  value: {
                      singleSelectOptionId: \"$IN_REVIEW_PROJECT_OPTION_ID\"
                      }
                    }
                  ) {
                    projectV2Item {
                      id
                    }
                  }
                }"
              gh api graphql --field query="$QUERY"
            done
        continue-on-error: true
