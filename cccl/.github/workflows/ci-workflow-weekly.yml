# SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: weekly

defaults:
  run:
    shell: bash --noprofile --norc -euo pipefail {0}

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * 6" # Saturdays at 12AM UTC, 8PM EST, 5PM PST

concurrency:
  group: ${{ github.workflow }}-on-${{ github.event_name }}-from-${{ github.ref_name }}

jobs:

  build-workflow:
    name: Build workflow from matrix
    runs-on: ubuntu-latest
    permissions:
      contents: read
    outputs:
      workflow: ${{ steps.build-workflow.outputs.workflow }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Build workflow
        id: build-workflow
        uses: ./.github/actions/workflow-build
        with:
          workflows: weekly
          slack_token: ${{ secrets.SLACK_NOTIFIER_BOT_TOKEN }}
          slack_log: ${{ secrets.SLACK_CHANNEL_CI_LOG }}
          slack_alert: ${{ secrets.SLACK_CHANNEL_CI_ALERT }}

  dispatch-groups-linux-two-stage:
    name: ${{ matrix.name }}
    if: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['linux_two_stage']['keys']) != '[]' }}
    needs: build-workflow
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        name: ${{ fromJSON(needs.build-workflow.outputs.workflow)['linux_two_stage']['keys'] }}
    uses: ./.github/workflows/workflow-dispatch-two-stage-group-linux.yml
    with:
      pc-array: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['linux_two_stage']['jobs'][matrix.name]) }}

  dispatch-groups-windows-two-stage:
    name: ${{ matrix.name }}
    if: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['windows_two_stage']['keys']) != '[]' }}
    needs: build-workflow
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        name: ${{ fromJSON(needs.build-workflow.outputs.workflow)['windows_two_stage']['keys'] }}
    uses: ./.github/workflows/workflow-dispatch-two-stage-group-windows.yml
    with:
      pc-array: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['windows_two_stage']['jobs'][matrix.name]) }}

  dispatch-groups-linux-standalone:
    name: ${{ matrix.name }}
    if: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['linux_standalone']['keys']) != '[]' }}
    needs: build-workflow
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        name: ${{ fromJSON(needs.build-workflow.outputs.workflow)['linux_standalone']['keys'] }}
    uses: ./.github/workflows/workflow-dispatch-standalone-group-linux.yml
    with:
      job-array: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['linux_standalone']['jobs'][matrix.name]) }}

  dispatch-groups-windows-standalone:
    name: ${{ matrix.name }}
    if: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['windows_standalone']['keys']) != '[]' }}
    needs: build-workflow
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        name: ${{ fromJSON(needs.build-workflow.outputs.workflow)['windows_standalone']['keys'] }}
    uses: ./.github/workflows/workflow-dispatch-standalone-group-windows.yml
    with:
      job-array: ${{ toJSON(fromJSON(needs.build-workflow.outputs.workflow)['windows_standalone']['jobs'][matrix.name]) }}

  verify-workflow:
    name: Verify and summarize workflow results
    if: ${{ always() && !cancelled() }}
    needs:
      - build-workflow
      - dispatch-groups-linux-two-stage
      - dispatch-groups-windows-two-stage
      - dispatch-groups-linux-standalone
      - dispatch-groups-windows-standalone
    permissions:
      contents: read
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Check workflow success
        id: check-workflow
        uses: ./.github/actions/workflow-results
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          slack_token: ${{ secrets.SLACK_NOTIFIER_BOT_TOKEN }}
          slack_log: ${{ secrets.SLACK_CHANNEL_CI_LOG }}
          slack_alert: ${{ secrets.SLACK_CHANNEL_CI_ALERT }}
