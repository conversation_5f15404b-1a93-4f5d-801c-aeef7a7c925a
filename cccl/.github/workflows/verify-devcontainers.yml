name: Verify devcontainers

on:
  workflow_call:
    inputs:
      base_sha:
        type: string
        description: 'For PRs, set the base SHA to conditionally run this workflow only when relevant files are modified.'
        required: false


defaults:
  run:
    shell: bash -euo pipefail {0}

permissions:
  contents: read

jobs:
  get-devcontainer-list:
    name: Verify devcontainer files are up-to-date
    outputs:
      skip: ${{ steps.inspect-changes.outputs.skip }}
      devcontainers: ${{ steps.get-list.outputs.devcontainers }}
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Setup jq and yq
      run: |
        sudo apt-get update
        sudo apt-get install jq -y
        sudo wget -O /usr/local/bin/yq https://github.com/mikefarah/yq/releases/download/v4.34.2/yq_linux_amd64
        sudo chmod +x /usr/local/bin/yq
    - name: Run the script to generate devcontainer files
      run: |
        ./.devcontainer/make_devcontainers.sh --verbose --clean
    - name: Check for changes
      run: |
        if [[ $(git diff --stat) != '' || $(git status --porcelain | grep '^??') != '' ]]; then
          git diff --minimal
          git status --porcelain
          echo "::error:: Dev Container files are out of date or there are untracked files. Run the .devcontainer/make_devcontainers.sh script and commit the changes."
          exit 1
        else
          echo "::note::Dev Container files are up-to-date."
        fi
    - name: Inspect changes
      if: ${{ inputs.base_sha != '' }}
      id: inspect-changes
      env:
        BASE_SHA: ${{ inputs.base_sha }}
      run: |
        echo "Fetch history and determine merge base..."
        git fetch origin --unshallow -q
        git fetch origin $BASE_SHA -q
        merge_base_sha=$(git merge-base $GITHUB_SHA $BASE_SHA)

        echo "Head SHA: $GITHUB_SHA"
        echo "PR Base SHA: $BASE_SHA"
        echo "Merge Base SHA: $merge_base_sha"

        echo "Checking for changes to devcontainer/matrix files..."

        all_dirty_files=$(git diff --name-only "${merge_base_sha}" "${GITHUB_SHA}")
        echo "::group::All dirty files"
        echo "${all_dirty_files}"
        echo "::endgroup::"

        file_regex="^(.devcontainer|ci/matrix.yaml|.github/actions/workflow-build/build-workflow.py)"
        echo "Regex: ${file_regex}"

        relevant_dirty_files=$(echo "${all_dirty_files}" | grep -E "${file_regex}" || true)
        echo "::group::Relevant dirty files"
        echo "${relevant_dirty_files}"
        echo "::endgroup::"

        if [[ -z "${relevant_dirty_files}" ]]; then
          echo "No relevant changes detected. Skipping devcontainer testing."
          echo "skip=true" >> $GITHUB_OUTPUT
        else
          echo "Detected relevant changes. Continuing."
          echo "skip=false" >> $GITHUB_OUTPUT
        fi
    - name: Get list of devcontainer.json paths and names
      if: ${{ steps.inspect-changes.outputs.skip != 'true' }}
      id: get-list
      run: |
        devcontainers=$(find .devcontainer/ -name 'devcontainer.json' | while read -r devcontainer; do
          jq --arg path "$devcontainer" '{path: $path, name: .name}' "$devcontainer"
          done | jq -s -c .)
        echo "devcontainers=${devcontainers}" | tee --append "${GITHUB_OUTPUT}"

  verify-devcontainers:
    name: ${{matrix.devcontainer.name}}
    needs: get-devcontainer-list
    if: ${{ needs.get-devcontainer-list.outputs.skip != 'true' }}
    runs-on: linux-amd64-cpu4
    strategy:
      fail-fast: false
      matrix:
        devcontainer: ${{fromJson(needs.get-devcontainer-list.outputs.devcontainers)}}
    permissions:
      id-token: write
      contents: read
    steps:
    - name: Check out the code
      uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Install dependencies
      run: |
        # Add PPA for nodejs, devcontainer CLI requires a newer version:
        curl -fsSL https://deb.nodesource.com/setup_20.x -o /tmp/nodesource_setup.sh
        sudo bash /tmp/nodesource_setup.sh
        sudo apt-get update
        sudo apt-get install -y nodejs
        sudo npm install -g @devcontainers/cli

      # We don't really need sccache configured, but we need the AWS credentials envvars to be set
      # in order to avoid the devcontainer hanging waiting for GitHub authentication
    - name: Get AWS credentials for sccache bucket
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::279114543810:role/gha-oidc-NVIDIA
        aws-region: us-east-2
        role-duration-seconds: 43200 # 12 hours
    - name: Set environment variables
      run: |
        echo "SCCACHE_BUCKET=rapids-sccache-devs" >> $GITHUB_ENV
        echo "SCCACHE_REGION=us-east-2" >> $GITHUB_ENV
        echo "SCCACHE_IDLE_TIMEOUT=32768" >> $GITHUB_ENV
        echo "SCCACHE_S3_USE_SSL=true" >> $GITHUB_ENV
        echo "SCCACHE_S3_NO_CREDENTIALS=false" >> $GITHUB_ENV

    - name: Run in devcontainer
      uses: devcontainers/ci@v0.3
      with:
        push: never
        configFile: ${{ matrix.devcontainer.path }}
        env: |
          SCCACHE_REGION=${{ env.SCCACHE_REGION }}
          AWS_ACCESS_KEY_ID=${{ env.AWS_ACCESS_KEY_ID }}
          AWS_SESSION_TOKEN=${{ env.AWS_SESSION_TOKEN }}
          AWS_SECRET_ACCESS_KEY=${{ env.AWS_SECRET_ACCESS_KEY }}
        runCmd: |
          .devcontainer/verify_devcontainer.sh
