name: "Run Linux Job"
description: "Run a job on a Linux runner."

inputs:
  id:
    description: "A unique identifier."
    required: true
  command:
    description: "The command to run."
    required: true
  image:
    description: "The Docker image to use."
    required: true
  runner:
    description: "The GHA runs-on value."
    required: true
  cuda:
    description: "The CUDA version to use when selecting a devcontainer."
    required: true
  host:
    description: "The host compiler to use when selecting a devcontainer."
    required: true

runs:
  using: "composite"
  steps:
    - name: Install dependencies
      shell: sh
      run: |
        # Install script dependencies
        sudo apt update
        sudo apt install -y --no-install-recommends tree git
    - name: Checkout repo
      uses: actions/checkout@v4
      with:
        path: ${{github.event.repository.name}}
        persist-credentials: false
    - name: Add NVCC problem matcher
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "::add-matcher::${{github.event.repository.name}}/.github/problem-matchers/problem-matcher.json"
    - name: Get AWS credentials for sccache bucket
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::279114543810:role/gha-oidc-NVIDIA
        aws-region: us-east-2
        role-duration-seconds: 43200 # 12 hours
    - name: Run command # Do not change this step's name, it is checked in parse-job-times.py
      shell: bash --noprofile --norc -euo pipefail {0}
      env:
        CI: true
        RUNNER: "${{inputs.runner}}"
        IMAGE: "${{inputs.image}}"
        # Dereferencing the command from an env var instead of a GHA input avoids issues with escaping
        # semicolons and other special characters (e.g. `-arch "60;70;80"`).
        COMMAND: "${{inputs.command}}"
        AWS_ACCESS_KEY_ID: "${{env.AWS_ACCESS_KEY_ID}}"
        AWS_SESSION_TOKEN: "${{env.AWS_SESSION_TOKEN}}"
        AWS_SECRET_ACCESS_KEY: "${{env.AWS_SECRET_ACCESS_KEY}}"
      run: |
        mkdir artifacts

        cat <<'EOF' > ci.sh
        #! /usr/bin/env bash
        set -euo pipefail
        echo -e "\e[1;34mRunning as '$(whoami)' user in $(pwd):\e[0m"
        echo -e "\e[1;34m${COMMAND}\e[0m"
        eval "${COMMAND}"
        exit_code=$?
        if [[ "$exit_code" -ne 0 ]]; then
          echo -e "::group::️❗ \e[1;31mInstructions to Reproduce CI Failure Locally\e[0m"
          echo "::error:: To replicate this failure locally, follow the steps below:"
          echo "1. Clone the repository, and navigate to the correct branch and commit:"
          echo "   git clone --branch $GITHUB_REF_NAME --single-branch https://github.com/$GITHUB_REPOSITORY.git && cd $(echo $GITHUB_REPOSITORY | cut -d'/' -f2) && git checkout $GITHUB_SHA"
          echo ""
          echo "2. Run the failed command inside the same Docker container used by this CI job:"
          echo "   .devcontainer/launch.sh -d -c ${{inputs.cuda}} -H ${{inputs.host}} -- ${COMMAND}"
          echo ""
          echo "For additional information, see:"
          echo "   - DevContainer Documentation: https://github.com/NVIDIA/cccl/blob/main/.devcontainer/README.md"
          echo "   - Continuous Integration (CI) Overview: https://github.com/NVIDIA/cccl/blob/main/ci-overview.md"
          exit $exit_code
        fi

        # Copy any artifacts we want to preserve out of the container:
        results_dir=/artifacts

        # Finds a matching file in the repo directory and copies it to the results directory.
        find_and_copy() {
          filename="$1"
          filepath="$(find . -name "${filename}" -print -quit)"
          if [[ -z "$filepath" ]]; then
            echo "${filename} does not exist in repo directory."
            return 1
          fi
          cp -v "$filepath" "$results_dir"
        }

        find_and_copy "sccache_stats.json" || :
        EOF

        chmod +x ci.sh

        # The devcontainer will mount this path to the home directory:
        readonly aws_dir="${{github.event.repository.name}}/.aws"
        mkdir "${aws_dir}";

        cat <<EOF > "${aws_dir}/config"
        [default]
        bucket=rapids-sccache-devs
        region=us-east-2
        EOF

        cat <<EOF > "${aws_dir}/credentials"
        [default]
        aws_access_key_id=$AWS_ACCESS_KEY_ID
        aws_session_token=$AWS_SESSION_TOKEN
        aws_secret_access_key=$AWS_SECRET_ACCESS_KEY
        EOF

        chmod 0600 "${aws_dir}/credentials"
        chmod 0664 "${aws_dir}/config"

        declare -a gpu_request=()

        # Explicitly pass which GPU to use if on a GPU runner
        if [[ "${RUNNER}" = *"-gpu-"* ]]; then
          gpu_request+=(--gpus "device=${NVIDIA_VISIBLE_DEVICES}")
        fi

        # If the image contains "cudaXX.Yext"...
        if [[ "${IMAGE}" =~ cuda[0-9.]+ext ]]; then
          cuda_ext_request="--cuda-ext"
        fi

        # Launch this container using the host's docker daemon
        set -x
        ${{github.event.repository.name}}/.devcontainer/launch.sh \
          --docker \
          --cuda ${{inputs.cuda}} \
          --host ${{inputs.host}} \
          ${cuda_ext_request:-} \
          "${gpu_request[@]}" \
          --env "CI=$CI" \
          --env "AWS_ROLE_ARN=" \
          --env "COMMAND=$COMMAND" \
          --env "SCCACHE_IDLE_TIMEOUT=0" \
          --env "GITHUB_ENV=$GITHUB_ENV" \
          --env "GITHUB_SHA=$GITHUB_SHA" \
          --env "GITHUB_PATH=$GITHUB_PATH" \
          --env "GITHUB_OUTPUT=$GITHUB_OUTPUT" \
          --env "GITHUB_ACTIONS=$GITHUB_ACTIONS" \
          --env "GITHUB_REF_NAME=$GITHUB_REF_NAME" \
          --env "GITHUB_WORKSPACE=$GITHUB_WORKSPACE" \
          --env "GITHUB_REPOSITORY=$GITHUB_REPOSITORY" \
          --env "GITHUB_STEP_SUMMARY=$GITHUB_STEP_SUMMARY" \
          --volume "${{github.workspace}}/ci.sh:/ci.sh" \
          --volume "${{github.workspace}}/artifacts:/artifacts" \
          -- /ci.sh

    - name: Prepare job artifacts
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "Prepare job artifacts"
        result_dir="jobs/${{inputs.id}}"
        mkdir -p "$result_dir"

        touch "$result_dir/success"

        artifacts_exist="$(ls -A artifacts)"
        if [ "$artifacts_exist" ]; then
          cp -rv artifacts/* "$result_dir"
        fi

        echo "::group::Job artifacts"
        tree "$result_dir"
        echo "::endgroup::"

    - name: Upload job artifacts
      uses: actions/upload-artifact@v4
      with:
        name: jobs-${{inputs.id}}
        path: jobs
        compression-level: 0
