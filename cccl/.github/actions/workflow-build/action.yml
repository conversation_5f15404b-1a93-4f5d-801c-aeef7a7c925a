name: "CCCL Build Workflow"
description: "Parses a matrix definition and exports a set of dispatchable build/test/etc jobs."

inputs:
  workflows:
    description: "Space separated list of workflows in matrix file to run"
    required: true
  allow_override:
    description: "If true, the requested `workflows` will be ignored when a non-empty 'override' workflow exists in the matrix file."
    default: "false"
    required: false
  inspect_changes_script:
    description: "If defined, run this script to determine which projects/deps need to be tested."
    default: ""
    required: false
  inspect_changes_base_sha:
    description: "If defined, use this base ref for inspect-changes script."
    default: ""
    required: false
  matrix_file:
    description: "Path to the matrix file in the consumer repository."
    default: "ci/matrix.yaml"
    required: false
  matrix_parser:
    description: "Path to the matrix parser script (default if blank: build-workflow.py from action dir)"
    default: ""
    required: false
  slack_token:
    description: "The Slack token to use for notifications. No notifications will be sent if not provided."
    required: false
  slack_log:
    description: "Slack channel ID for verbose notifications."
    required: false
  slack_alert:
    description: "Slack channel ID for alert notifications."
    required: false

outputs:
  workflow:
    description: "The dispatchable workflows"
    value: ${{ steps.outputs.outputs.WORKFLOW }}

runs:
  using: "composite"
  steps:

    - name: Send Slack log notification
      if: ${{inputs.slack_token != '' && inputs.slack_log != '' }}
      uses: slackapi/slack-github-action@v1.26.0
      env:
        SLACK_BOT_TOKEN: ${{ inputs.slack_token }}
        WORKFLOW_TYPE: ${{ github.workflow }} # nightly, weekly, pr, etc.
        SUMMARY_URL: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
      with:
        channel-id: ${{ inputs.slack_log }}
        slack-message: |
          Workflow '${{ env.WORKFLOW_TYPE }}' starting...

          Details: ${{ env.SUMMARY_URL }}

    - name: Inspect changes
      if: ${{ inputs.inspect_changes_script != '' && inputs.inspect_changes_base_sha != '' }}
      id: inspect-changes
      shell: bash --noprofile --norc -euo pipefail {0}
      env:
        base_ref: ${{ inputs.inspect_changes_base_sha }}
      run: |
        echo "Running inspect-changes script..."
        ${{ inputs.inspect_changes_script }} ${base_ref} ${GITHUB_SHA}
        echo "Exporting summary..."
        mkdir workflow
        cp ${GITHUB_STEP_SUMMARY} workflow/changes.md

    - name: Parse matrix file into a workflow
      id: build-workflow
      shell: bash --noprofile --norc -euo pipefail {0}
      env:
        allow_override: ${{ inputs.allow_override == 'true' && '--allow-override' || ''}}
        dirty_projects_flag: ${{ inputs.inspect_changes_script != '' && '--dirty-projects' || ''}}
        dirty_projects: ${{ inputs.inspect_changes_script != '' && steps.inspect-changes.outputs.dirty_projects || ''}}
        matrix_parser: ${{ inputs.matrix_parser && inputs.matrix_parser || '${GITHUB_ACTION_PATH}/build-workflow.py' }}
      run: |
        echo "Parsing matrix file into a workflow..."

        ${{ env.matrix_parser }} ${{ inputs.matrix_file }}           \
            --workflows ${{ inputs.workflows }}                      \
            ${{ env.allow_override }}                                \
            ${{ env.dirty_projects_flag }} ${{ env.dirty_projects }}

        echo "::group::Workflow"
        cat workflow/workflow.json
        echo "::endgroup::"

        echo "::group::Runners"
        cat workflow/runner_summary.json | jq -r '"# \(.heading)\n\n\(.body)"' | tee -a "${GITHUB_STEP_SUMMARY}"
        echo "::endgroup::"

        echo "::group::Job List"
        cat workflow/job_list.txt
        echo "::endgroup::"

    - name: Create dispatch workflows
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        "${GITHUB_ACTION_PATH}/prepare-workflow-dispatch.py" workflow/workflow.json

        echo "::group::Dispatch Workflows"
        cat workflow/dispatch.json
        echo "::endgroup::"

    - name: Set outputs
      id: outputs
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "::group::GHA Output: WORKFLOW"
        printf "WORKFLOW=%s\n" "$(cat workflow/dispatch.json | jq -c '.')" | tee -a "${GITHUB_OUTPUT}"
        echo "::endgroup::"

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: workflow
        path: workflow/
        compression-level: 0

    - name: Send Slack error notification
      if: ${{ failure() && inputs.slack_token != '' && (inputs.slack_alert != '' || inputs.slack_log != '') }}
      uses: slackapi/slack-github-action@v1.26.0
      env:
        SLACK_BOT_TOKEN: ${{ inputs.slack_token }}
        WORKFLOW_TYPE: ${{ github.workflow }} # nightly, weekly, pr, etc.
        SUMMARY_URL: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
        CHANNEL_SEP: ${{ (inputs.slack_log != '' && inputs.slack_alert != '') && ',' || ''}}
      with:
        channel-id: '${{ inputs.slack_log }}${{env.CHANNEL_SEP}}${{ inputs.slack_alert }}'
        slack-message: |
          Workflow '${{ env.WORKFLOW_TYPE }}' encountered an error while preparing to run.

          Details: ${{ env.SUMMARY_URL }}
