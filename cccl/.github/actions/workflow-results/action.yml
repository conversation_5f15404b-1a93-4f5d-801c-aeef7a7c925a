name: "CCCL Workflow Sentinel"
description: "Check the results of the dispatched jobs and comment on the PR."

inputs:
  github_token:
    description: "The GitHub token to use for commenting on the PR. No comment will be made if not provided."
    required: false
  pr_number:
    description: "The PR number to comment on, if applicable. No comment will be made if not provided."
    required: false
  slack_token:
    description: "The Slack token to use for notifications. No notifications will be sent if not provided."
    required: false
  slack_log:
    description: "Slack channel ID for verbose notifications."
    required: false
  slack_alert:
    description: "Slack channel ID for alert notifications."
    required: false

outputs:
  success:
    description: "Whether any jobs failed."
    value: ${{ steps.check-success.outputs.success }}

runs:
  using: "composite"
  steps:

    - name: Download workflow artifacts
      uses: actions/download-artifact@v4
      with:
        name: workflow
        path: workflow/

    - name: Download job artifacts
      continue-on-error: true # This may fail if no jobs succeed. The checks below will catch this.
      uses: actions/download-artifact@v4
      with:
        path: jobs
        pattern: jobs-*
        merge-multiple: true

    - name: Clean up job artifacts
      continue-on-error: true
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        # Fix artifacts written on windows:
        echo "::group::Fixing line endings in job artifacts"
        sudo apt-get update
        sudo apt-get install -y dos2unix
        find jobs -type f -exec dos2unix -v {} \;
        echo "::endgroup::"

        echo "::group::Job artifacts"
        tree jobs
        echo "::endgroup::"

    - name: Fetch workflow job info
      if: ${{ inputs.github_token != ''}}
      continue-on-error: true
      uses: actions/github-script@v7
      with:
        github-token: ${{ inputs.github_token }}
        script: |
          const fs = require('fs');

          const owner = context.repo.owner;
          const repo = context.repo.repo;
          const runId = context.runId;

          github.paginate(
            'GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs?filter=all',
            {
              owner: owner,
              repo: repo,
              run_id: runId
            }
          )
          .then(jobs => {
            console.log('::group::Jobs JSON');
            console.log(JSON.stringify(jobs, null, 2));
            console.log('::endgroup::');
            fs.mkdirSync("results", { recursive: true });
            fs.writeFileSync('results/jobs.json', JSON.stringify(jobs, null, 2));
            console.log(`Fetched ${jobs.length} jobs and saved to results/jobs.json`);
          })
          .catch(error => {
            console.error(error);
          });

    - name: Parse job times
      continue-on-error: true
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "Parsing job times..."
        python3 "${GITHUB_ACTION_PATH}/parse-job-times.py" workflow/workflow.json results/jobs.json

    - name: Prepare execution summary
      continue-on-error: true
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "Generating execution summary..."
        python3 "${GITHUB_ACTION_PATH}/prepare-execution-summary.py" workflow/workflow.json results/job_times.json

    - name: Prepare final summary
      id: final-summary
      continue-on-error: true
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "::group::Final Summary"
        python3 "${GITHUB_ACTION_PATH}/final-summary.py" | tee final_summary.md
        echo "::endgroup::"

        # This allows multiline strings and special characters to be passed through the GHA outputs:
        url_encode_string() {
          python3 -c "import sys; from urllib.parse import quote; print(quote(sys.stdin.read()))"
        }

        echo "::group::GHA Output: SUMMARY"
        printf "SUMMARY=%s\n" "$(cat final_summary.md | url_encode_string)" | tee -a "${GITHUB_OUTPUT}"
        echo "::endgroup::"

        echo "::group::GHA Output: EXEC_SUMMARY"
        printf "EXEC_SUMMARY=%s\n" "$(cat execution/heading.txt)" | tee -a "${GITHUB_OUTPUT}"
        echo "::endgroup::"

        cp final_summary.md ${GITHUB_STEP_SUMMARY}

    - name: Comment on PR
      if: ${{ !cancelled() && inputs.pr_number != '' && inputs.github_token != ''}}
      continue-on-error: true
      env:
        PR_NUMBER: ${{ fromJSON(inputs.pr_number) }}
        COMMENT_BODY: ${{ steps.final-summary.outputs.SUMMARY }}
      uses: actions/github-script@v7
      with:
        github-token: ${{ inputs.github_token }}
        script: |
          const pr_number = process.env.PR_NUMBER;
          const owner = context.repo.owner;
          const repo = context.repo.repo;
          // Decode URL-encoded string for proper display in comments
          const commentBody = decodeURIComponent(process.env.COMMENT_BODY);
          console.log('::group::Commenting on PR #' + pr_number + ' with the following message:')
          console.log(commentBody);
          console.log('::endgroup::');
          github.rest.issues.createComment({
              owner: owner,
              repo: repo,
              issue_number: pr_number,
              body: commentBody
          });

    - name: Check for job success
      id: check-success
      shell: bash --noprofile --norc -euo pipefail {0}
      run: |
        echo "::group::Checking for success artifacts"
        "${GITHUB_ACTION_PATH}/verify-job-success.py" workflow/job_ids.json
        result=$?
        echo "::endgroup::"

        if [[ $result -ne 0 ]]; then
          echo "success=false" >> "${GITHUB_OUTPUT}"
          exit 1
        fi

        if [ -f workflow/override.json ]; then
          echo "::notice::Workflow matrix was overridden. Failing jobs."
          echo "Override matrix:"
          cat workflow/override.json | jq -c '.'
          echo "success=false" >> "${GITHUB_OUTPUT}"
          exit 1
        fi

        echo "success=true" >> "${GITHUB_OUTPUT}"

    - name: Send Slack log notification
      if: ${{ always() && inputs.slack_token != '' && inputs.slack_log != '' }}
      uses: slackapi/slack-github-action@v1.26.0
      env:
        SLACK_BOT_TOKEN: ${{ inputs.slack_token }}
        WORKFLOW_TYPE: ${{ github.workflow }} # nightly, weekly, pr, etc.
        STATUS: ${{ steps.check-success.outcome }}
        EXEC_SUMMARY: ${{ steps.final-summary.outputs.EXEC_SUMMARY }}
        SUMMARY_URL: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
      with:
        channel-id: ${{ inputs.slack_log }}
        slack-message: |
          Workflow '${{ env.WORKFLOW_TYPE }}' has finished with status `${{ env.STATUS }}`:

          ${{ env.EXEC_SUMMARY }}

          Details: ${{ env.SUMMARY_URL }}

    - name: Send Slack alert notification
      if: ${{ failure() && inputs.slack_token != '' && inputs.slack_alert != '' }}
      uses: slackapi/slack-github-action@v1.26.0
      env:
        SLACK_BOT_TOKEN: ${{ inputs.slack_token }}
        WORKFLOW_TYPE: ${{ github.workflow }} # nightly, weekly, pr, etc.
        EXEC_SUMMARY: ${{ steps.final-summary.outputs.EXEC_SUMMARY }}
        SUMMARY_URL: https://github.com/${{github.repository}}/actions/runs/${{github.run_id}}
      with:
        channel-id: ${{ inputs.slack_alert }}
        slack-message: |
          Workflow '${{ env.WORKFLOW_TYPE }}' has failed:

          ${{ env.EXEC_SUMMARY }}

          Details: ${{ env.SUMMARY_URL }}
