{"shutdownAction": "stopContainer", "image": "rapidsai/devcontainers:25.06-cpp-gcc13-cuda12.8", "hostRequirements": {"gpu": "optional"}, "initializeCommand": ["/bin/bash", "-c", "mkdir -m 0755 -p ${localWorkspaceFolder}/.{aws,cache,config}; mkdir -m 0755 -p ${localWorkspaceFolder}/build; if test -z ${localEnv:WSLENV}; then docker volume create --driver local --opt type=none --opt device=${localWorkspaceFolder}/build --opt o=bind cccl-build; else docker volume create cccl-build; fi;"], "containerEnv": {"SCCACHE_REGION": "us-east-2", "SCCACHE_BUCKET": "rapids-sccache-devs", "AWS_ROLE_ARN": "arn:aws:iam::279114543810:role/nv-gha-token-sccache-devs", "HISTFILE": "${containerWorkspaceFolder}/.cache/._bash_history", "DEVCONTAINER_NAME": "cuda12.8-gcc13", "CCCL_CUDA_VERSION": "12.8", "CCCL_HOST_COMPILER": "gcc", "CCCL_HOST_COMPILER_VERSION": "13", "CCCL_BUILD_INFIX": "cuda12.8-gcc13", "CCCL_CUDA_EXTENDED": "false"}, "workspaceFolder": "/home/<USER>/${localWorkspaceFolderBasename}", "workspaceMount": "source=${localWorkspaceFolder},target=/home/<USER>/${localWorkspaceFolderBasename},type=bind,consistency=consistent", "mounts": ["source=${localWorkspaceFolder}/.aws,target=/home/<USER>/.aws,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/.cache,target=/home/<USER>/.cache,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/.config,target=/home/<USER>/.config,type=bind,consistency=consistent", "source=cccl-build,target=/home/<USER>/cccl/build"], "customizations": {"vscode": {"extensions": ["llvm-vs-code-extensions.vscode-clangd", "seaube.clangformat", "nvidia.nsight-vscode-edition", "ms-vscode.cmake-tools"], "settings": {"editor.defaultFormatter": "seaube.clangformat", "editor.formatOnSave": true, "clang-format.executable": "/usr/bin/clang-format", "clangd.arguments": ["--header-insertion=never", "--compile-commands-dir=${workspaceFolder}"], "files.eol": "\n", "files.trimTrailingWhitespace": true}}}, "name": "cuda12.8-gcc13"}