{"image": "rapidsai/devcontainers:25.06-cpp-mambaforge-ubuntu22.04", "runArgs": ["--rm", "--name", "${localEnv:USER:anon}-${localWorkspaceFolderBasename}-rapids-25.06-cuda12.8-conda"], "hostRequirements": {"gpu": "optional"}, "features": {"ghcr.io/rapidsai/devcontainers/features/rapids-build-utils:25.6": {}}, "overrideFeatureInstallOrder": ["ghcr.io/rapidsai/devcontainers/features/rapids-build-utils"], "containerEnv": {"CI": "${localEnv:CI}", "CUDAARCHS": "70-real", "CUDA_VERSION": "12.8", "DEFAULT_CONDA_ENV": "rapids", "PYTHONSAFEPATH": "1", "PYTHONUNBUFFERED": "1", "PYTHONDONTWRITEBYTECODE": "1", "PYTHON_PACKAGE_MANAGER": "conda", "SCCACHE_REGION": "us-east-2", "SCCACHE_BUCKET": "rapids-sccache-devs", "AWS_ROLE_ARN": "arn:aws:iam::279114543810:role/nv-gha-token-sccache-devs", "HISTFILE": "/home/<USER>/.cache/._bash_history", "LIBCUDF_KERNEL_CACHE_PATH": "/home/<USER>/cudf/cpp/build/latest/jitify_cache", "RAPIDS_LIBS": "${localEnv:RAPIDS_LIBS}", "RAPIDS_cmake_GIT_REPO": "${localEnv:RAPIDS_cmake_GIT_REPO}", "RAPIDS_rmm_GIT_REPO": "${localEnv:RAPIDS_rmm_GIT_REPO}", "RAPIDS_ucxx_GIT_REPO": "${localEnv:RAPIDS_ucxx_GIT_REPO}", "RAPIDS_kvikio_GIT_REPO": "${localEnv:RAPIDS_kvikio_GIT_REPO}", "RAPIDS_cudf_GIT_REPO": "${localEnv:RAPIDS_cudf_GIT_REPO}", "RAPIDS_raft_GIT_REPO": "${localEnv:RAPIDS_raft_GIT_REPO}", "RAPIDS_cuvs_GIT_REPO": "${localEnv:RAPIDS_cuvs_GIT_REPO}", "RAPIDS_cumlprims_mg_GIT_REPO": "${localEnv:RAPIDS_cumlprims_mg_GIT_REPO}", "RAPIDS_cuml_GIT_REPO": "${localEnv:RAPIDS_cuml_GIT_REPO}", "RAPIDS_cugraph_GIT_REPO": "${localEnv:RAPIDS_cugraph_GIT_REPO}", "RAPIDS_cugraph_gnn_GIT_REPO": "${localEnv:RAPIDS_cugraph_gnn_GIT_REPO}"}, "initializeCommand": ["/bin/bash", "-c", "mkdir -m 0755 -p ${localWorkspaceFolder}/.{aws,cache,config} ${localWorkspaceFolder}/ci/rapids/.{conda,log/devcontainer-utils} ${localWorkspaceFolder}/ci/rapids/.repos/{rmm,kvikio,ucxx,cudf,raft,cuvs,cuml,cugraph,cugraph-gnn}"], "postCreateCommand": ["/bin/bash", "-c", "if [ ${CI:-false} = 'false' ]; then . /home/<USER>/cccl/ci/rapids/post-create-command.sh; fi"], "postAttachCommand": ["/bin/bash", "-c", "if [ ${CODESPACES:-false} = 'true' ]; then . devcontainer-utils-post-attach-command; fi"], "workspaceFolder": "/home/<USER>/${localWorkspaceFolderBasename}", "workspaceMount": "source=${localWorkspaceFolder},target=/home/<USER>/${localWorkspaceFolderBasename},type=bind,consistency=consistent", "mounts": ["source=${localWorkspaceFolder}/.aws,target=/home/<USER>/.aws,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/.cache,target=/home/<USER>/.cache,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/.config,target=/home/<USER>/.config,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/rmm,target=/home/<USER>/rmm,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/kvikio,target=/home/<USER>/kvikio,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/ucxx,target=/home/<USER>/ucxx,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/cudf,target=/home/<USER>/cudf,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/raft,target=/home/<USER>/raft,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/cuvs,target=/home/<USER>/cuvs,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/cuml,target=/home/<USER>/cuml,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/cugraph,target=/home/<USER>/cugraph,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.repos/cugraph-gnn,target=/home/<USER>/cugraph-gnn,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.conda,target=/home/<USER>/.conda,type=bind,consistency=consistent", "source=${localWorkspaceFolder}/ci/rapids/.log/devcontainer-utils,target=/var/log/devcontainer-utils,type=bind,consistency=consistent"], "customizations": {"vscode": {"extensions": ["augustocdias.tasks-shell-input", "ms-python.flake8", "nvidia.nsight-vscode-edition"], "files.watcherExclude": {"**/build/**": true, "**/_skbuild/**": true, "**/target/**": true, "/home/<USER>/.aws/**/*": true, "/home/<USER>/.cache/**/*": true, "/home/<USER>/.conda/**/*": true, "/home/<USER>/.local/share/**/*": true, "/home/<USER>/.vscode-server/**/*": true}, "search.exclude": {"**/build/**": true, "**/_skbuild/**": true, "**/*.code-search": true, "/home/<USER>/.aws/**/*": true, "/home/<USER>/.cache/**/*": true, "/home/<USER>/.conda/**/*": true, "/home/<USER>/.local/share/**/*": true, "/home/<USER>/.vscode-server/**/*": true}}}}