workflows:
  # If any jobs appear here, they will be executed instead of `pull_request' for PRs.
  # This is useful for limiting resource usage when a full matrix is not needed.
  # The branch protection checks will fail when using this override workflow.
  #
  # Example:
  # override:
  #   - {jobs: ['test'], project: 'thrust', std: 17, ctk: 'curr', cxx: ['gcc12', 'clang16']}
  #
  override:

  pull_request:
    # Old CTK/compiler
    - {jobs: ['build'], std: 'minmax', ctk: '12.0', cxx: ['gcc7', 'gcc9', 'clang14', 'msvc2019']}
    # Current CTK build-only
    - {jobs: ['build'], std: 'max', cxx: ['gcc7', 'gcc8', 'gcc9']}
    - {jobs: ['build'], std: 'all', cxx: ['gcc10', 'gcc11', 'gcc12']}
    - {jobs: ['build'], std: 'all', cxx: ['clang14', 'clang15', 'clang16', 'clang17', 'clang18']}
    - {jobs: ['build'], std: 'max', cxx: ['msvc2019']}
    - {jobs: ['build'], std: 'all', cxx: ['gcc', 'clang', 'msvc']}
    # Current CTK testing:
    - {jobs: ['test'],  project: ['thrust'],     std: 'max', cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtx4090'}
    - {jobs: ['test'],  project: ['libcudacxx'], std: 'max', cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtx2080'}
    # Split up cub tests:
    - {jobs: ['test_nolid', 'test_lid0'], project: ['cub'], std: 'max', cxx: ['gcc'],           gpu: 'rtxa6000'}
    - {jobs: ['test_lid1', 'test_lid2'],  project: ['cub'], std: 'max', cxx: ['gcc'],           gpu: 'rtxa6000'}
    - {jobs: ['test_nolid', 'test_lid0'], project: ['cub'], std: 'max', cxx: ['clang', 'msvc'], gpu: 'rtxa6000'}
    # Modded builds:
    - {jobs: ['build'], std: 'all', ctk: '12.8', cxx: 'nvhpc'}
    - {jobs: ['build'], std: 'max', cxx: ['gcc', 'clang'], cpu: 'arm64'}
    - {jobs: ['build'], std: 'max', cxx: 'gcc', sm: ['90;90a;100']}
    - {jobs: ['test_nolid', 'test_lid0'], project: 'cub',                   std: 'max', gpu: 'h100', sm: 'gpu' }
    - {jobs: ['test_gpu'],                project: 'thrust',                std: 'max', gpu: 'h100', sm: 'gpu' }
    - {jobs: ['test'],                    project: ['libcudacxx', 'cudax'], std: 'max', gpu: 'h100', sm: 'gpu' }
    # Test Thrust 32-bit-only dispatch here, since it's most likely to break. 64-bit-only is tested in nightly.
    - {jobs: ['test_gpu'],  project: 'thrust', cmake_options: '-DTHRUST_DISPATCH_TYPE=Force32bit', gpu: 'rtx4090'}
    # default_projects: clang-cuda
    - {jobs: ['build'], std: 'all', cudacxx: 'clang', cxx: 'clang'}
    # Disabled; see discussion on #3633. Should be fixed in clang-20.
#    - {jobs: ['build'], project: 'libcudacxx', std: 'max', cudacxx: 'clang', cxx: 'clang', sm: '90'}
#    - {jobs: ['build'], project: 'libcudacxx', std: 'max', cudacxx: 'clang', cxx: 'clang', sm: '90a'}
    # nvrtc:
    - {jobs: ['nvrtc'], project: 'libcudacxx', std: 'all', gpu: 'rtx2080', sm: 'gpu'}
    # verify-codegen:
    - {jobs: ['verify_codegen'], project: 'libcudacxx'}
    # cudax has different CTK reqs:
    - {jobs: ['build'], project: 'cudax', ctk: ['12.0'], std: 20,    cxx: ['msvc14.39', 'gcc10', 'clang14']}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 20,    cxx: ['gcc10', 'gcc11', 'gcc12']}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 20,    cxx: ['clang14', 'clang15', 'clang16', 'clang17', 'clang18']}
    - {jobs: ['build'], project: 'cudax', ctk: ['12.8'], std: 'all', cxx: ['nvhpc']}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 20,    cxx: ['msvc']}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 17,    cxx: ['gcc'], sm: "90"}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 20,    cxx: ['gcc'], sm: "90a"}
    - {jobs: ['build'], project: 'cudax', ctk: ['curr'], std: 'all', cxx: ['gcc', 'clang'], cpu: 'arm64'}
    - {jobs: ['test'],  project: 'cudax', ctk: ['curr'], std: 20,    cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtx2080'}
    # Python and c/parallel jobs:
    - {jobs: ['test'], project: ['cccl_c_parallel', 'python'], gpu: 'rtx2080'}
    # cccl-infra:
    - {jobs: ['infra'], project: 'cccl', ctk: '12.0', cxx: ['gcc12', 'clang14'], gpu: 'rtx2080'}
    - {jobs: ['infra'], project: 'cccl', ctk: 'curr', cxx: ['gcc',   'clang'],   gpu: 'rtx2080'}
    # NVHPC stdpar smoke tests
    - {jobs: ['build'], project: 'stdpar', std: 'all', ctk: '12.8', cxx: 'nvhpc', cpu: ['amd64', 'arm64']}

  nightly:
    # Edge-case jobs
    - {jobs: ['limited'], project: 'cub', std: 17, gpu: 'rtx2080'}
    - {jobs: ['test_gpu'],  project: 'thrust', cmake_options: '-DTHRUST_DISPATCH_TYPE=Force32bit', gpu: 'rtx4090'}
    - {jobs: ['test_gpu'],  project: 'thrust', cmake_options: '-DTHRUST_DISPATCH_TYPE=Force64bit', gpu: 'rtx4090'}
    # Old CTK/compiler
    - {jobs: ['build'], std: 'all', ctk: '12.0', cxx: ['gcc7', 'gcc8', 'gcc9', 'clang14', 'msvc2019']}
    - {jobs: ['build'], std: 'all', ctk: '12.0', cxx: ['gcc11'], sm: '60;70;80;90'}
    # Current CTK build-only
    - {jobs: ['build'], std: 'all', cxx: ['gcc7', 'gcc8', 'gcc9', 'gcc10', 'gcc11', 'gcc12']}
    - {jobs: ['build'], std: 'all', cxx: ['clang14', 'clang15', 'clang16', 'clang17', 'clang18']}
    - {jobs: ['build'], std: 'all', cxx: ['msvc2019']}
    # Test current CTK
    - {jobs: ['test'],      project: 'cub',        std: 'all', cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtxa6000'}
    - {jobs: ['test'],      project: 'thrust',     std: 'all', cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtx4090'}
    - {jobs: ['test'],      project: 'libcudacxx', std: 'all', cxx: ['gcc', 'clang', 'msvc'], gpu: 'rtx2080'}
    # Modded builds:
    - {jobs: ['build'], std: 'all', ctk: '12.8', cxx: 'nvhpc'}
    - {jobs: ['build'], std: 'all', cxx: ['gcc', 'clang'], cpu: 'arm64'}
    - {jobs: ['build'], std: 'all', cxx: ['gcc'], sm: '90a'}
    - {jobs: ['test_nolid', 'test_lid0'], project: 'cub',                   std: 'max', gpu: ['v100', 't4', 'l4', 'h100'], sm: '70;75;89;90;100'}
    - {jobs: ['test_gpu'],                project: 'thrust',                std: 'max', gpu: ['v100', 't4', 'l4', 'h100'], sm: '70;75;89;90;100'}
    - {jobs: ['test'],                    project: ['libcudacxx', 'cudax'], std: 'max', gpu: ['v100', 't4', 'l4', 'h100'], sm: '70;75;89;90;100'}
    # default_projects: clang-cuda
    - {jobs: ['build'], std: 'all', cudacxx: 'clang', cxx: 'clang'}
    # Disabled; see discussion on #3633. Should be fixed in clang-20.
    # - {jobs: ['build'], project: 'libcudacxx', std: 'all', cudacxx: 'clang', cxx: 'clang', sm: '90'}
    # - {jobs: ['build'], project: 'libcudacxx', std: 'all', cudacxx: 'clang', cxx: 'clang', sm: '90a'}
    # cudax
    - {jobs: ['build'], project: 'cudax', ctk: ['12.0', 'curr'], std: 'all', cxx: ['gcc9', 'gcc10', 'gcc11', 'gcc12']}
    - {jobs: ['build'], project: 'cudax', ctk: [        'curr'], std: 'all', cxx: ['clang14', 'clang15', 'clang16', 'clang17', 'clang18']}
    - {jobs: ['build'], project: 'cudax', ctk: [        '12.8'], std: 'all', cxx: ['nvhpc']}
    - {jobs: ['build'], project: 'cudax', ctk: ['12.0'        ], std: '20',  cxx: ['msvc14.39']}
    - {jobs: ['build'], project: 'cudax', ctk: [        'curr'], std: '20',  cxx: ['msvc']}
    - {jobs: ['build'], project: 'cudax', ctk: ['12.0'        ], std: 'all', cxx: ['gcc12'], sm: "90"}
    - {jobs: ['build'], project: 'cudax', ctk: [        'curr'], std: 'all', cxx: ['gcc'], sm: "90a"}
    - {jobs: ['build'], project: 'cudax', ctk: [        'curr'], std: 'all', cxx: ['gcc', 'clang'], cpu: 'arm64'}
    - {jobs: ['test'],  project: 'cudax', ctk: ['12.0', 'curr'], std: 'all', cxx: ['gcc12']  , gpu: 'rtx2080'}
    - {jobs: ['test'],  project: 'cudax', ctk: [        'curr'], std: 'all', cxx: ['gcc']  ,   gpu: 'rtx2080'}
    - {jobs: ['test'],  project: 'cudax', ctk: ['12.0'        ], std: 'all', cxx: ['clang14'], gpu: 'rtx2080'}
    - {jobs: ['test'],  project: 'cudax', ctk: [        'curr'], std: 'all', cxx: ['clang'],   gpu: 'rtx2080'}
    # Python and c/parallel jobs:
    - {jobs: ['test'], project: ['cccl_c_parallel', 'python'], gpu: 'rtx2080'}
    # cccl-infra:
    - {jobs: ['infra'], project: 'cccl', ctk: '12.0', cxx: ['gcc12', 'clang14'], gpu: 'rtx2080'}
    - {jobs: ['infra'], project: 'cccl', ctk: 'curr', cxx: ['gcc',   'clang'],   gpu: 'rtx2080'}
    # NVHPC stdpar smoke tests
    - {jobs: ['build'], project: 'stdpar', std: 'all', ctk: '12.8', cxx: 'nvhpc', cpu: ['amd64', 'arm64']}

  weekly:
    - {jobs: ['compute_sanitizer'], project: 'cub', std: 'max', gpu: 'rtxa6000', sm: 'gpu', cmake_options: '-DCMAKE_CUDA_FLAGS=-lineinfo'}

  # Any generated jobs that match the entries in `exclude` will be removed from the final matrix for all workflows.
  exclude:
    # GPU runners are not available on Windows.
    - {jobs: ['test', 'test_gpu', 'test_nolid', 'test_lid0', 'test_lid1', 'test_lid2'], cxx: ['msvc2019', 'msvc14.39', 'msvc2022']}


#############################################################################################


# The version of the devcontainer images to use from https://hub.docker.com/r/rapidsai/devcontainers
devcontainer_version: '25.06'

# All supported C++ standards:
all_stds: [17, 20]

ctk_versions:
  12.0: { stds: [17, 20] }
  12.6: { stds: [17, 20] }
  12.8: { stds: [17, 20], aka: 'curr' }

device_compilers:
  nvcc: # Version / stds are taken from CTK
    name: 'nvcc'
    exe: 'nvcc'
  clang: # Requires cxx=clang. Version / stds are taken from cxx compiler.
    name: "ClangCUDA"
    exe: 'clang++'

host_compilers:
  gcc:
    name: 'GCC'
    container_tag: 'gcc'
    exe: 'g++'
    versions:
      7:  { stds: [17,   ] }
      8:  { stds: [17,   ] }
      9:  { stds: [17,   ] }
      10: { stds: [17, 20] }
      11: { stds: [17, 20] }
      12: { stds: [17, 20] }
      13: { stds: [17, 20] }
  clang:
    name: 'Clang'
    container_tag: 'llvm'
    exe: 'clang++'
    versions:
      14: { stds: [17, 20] }
      15: { stds: [17, 20] }
      16: { stds: [17, 20] }
      17: { stds: [17, 20] }
      18: { stds: [17, 20] }
      19: { stds: [17, 20] }
  msvc:
    name: 'MSVC'
    container_tag: 'cl'
    exe: cl
    versions:
      14.29: { stds: [ 17,   ], aka: '2019' }
      14.39: { stds: [ 17, 20]} # CTK 12.0 doesn't recognize >14.39 as MSVC 2022.
      14.42: { stds: [ 17, 20], aka: '2022' }
  nvhpc:
    name: 'NVHPC'
    container_tag: 'nvhpc'
    exe: nvc++
    versions:
      25.3: { stds: [17, 20 ] }

# Jobs support the following properties:
#
# - gpu: Whether the job requires a GPU runner. Default is false.
# - name: The human-readable name of the job. Default is the capitalized job key.
# - needs:
#   - A list of jobs that must be completed before this job can run. Default is an empty list.
#   - These are automatically added if needed:
#     - Eg. "jobs: ['test']" in the workflow def will also create the required 'build' jobs.
# - invoke:
#   - Map the job type to the script invocation spec:
#     - prefix: The script invocation prefix. Default is the job name.
#     - args: Additional arguments to pass to the script. Default is no args.
#   - The script is invoked either:
#     linux:   `ci/windows/<spec[prefix]>_<project>.ps1 <spec[args]>`
#     windows: `ci/<spec[prefix]>_<project>.sh <spec[args]>`
jobs:
  # General:
  build:        { gpu: false }
  test:         { gpu: true, needs: 'build' }

  test_nobuild: { gpu: true, name: 'Test', invoke: { prefix: 'test' } }

  compute_sanitizer: { gpu: true, name: 'ComputeSanitizer', needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer' } }

  # CCCL:
  infra: { gpu: true } # example project launches a kernel

  # libcudacxx:
  nvrtc: { gpu: true, name: 'NVRTC' }
  verify_codegen: { gpu: false, name: 'VerifyCodegen' }

  # CUB:
  # NoLid -> The string `lid_X` doesn't appear in the test name. Mostly warp/block tests, old device tests, and examples.
  test_nolid: { name: 'TestGPU',      gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-no-lid'} }
  # CUB uses `lid` to indicate launch strategies: whether CUB algorithms are:
  # - launched from the host (lid0):
  test_lid0:  { name: 'HostLaunch',   gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-lid0'} }
  # - launched from the device (lid1):
  test_lid1:  { name: 'DeviceLaunch', gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-lid1'} }
  # - captured in a CUDA graph for deferred launch (lid2):
  test_lid2:  { name: 'GraphCapture', gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-lid2'} }
  # Limited build reduces the number of runtime test cases, available device memory, etc, and may be used
  # to reduce test runtime in limited environments.
  limited:    { name: "SmallGMem",   gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-limited'} }
  # Compute sanitizer jobs:
  compute_mem_nolid:  { name: 'CSMem-TestGPU',       gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-memcheck  -no-lid'} }
  compute_mem_lid0:   { name: 'CSMem-HostLaunch',    gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-memcheck  -lid0'} }
  compute_race_nolid: { name: 'CSRace-TestGPU',      gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-racecheck -no-lid'} }
  compute_race_lid0:  { name: 'CSRace-HostLaunch',   gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-racecheck -lid0'} }
  compute_init_nolid: { name: 'CSInit-TestGPU',      gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-initcheck -no-lid'} }
  compute_init_lid0:  { name: 'CSInit-HostLaunch',   gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-initcheck -lid0'} }
  compute_sync_nolid: { name: 'CSSync-TestGPU',      gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-synccheck -no-lid'} }
  compute_sync_lid0:  { name: 'CSSync-HostLaunch',   gpu: true, needs: 'build', invoke: { prefix: 'test', args: '-compute-sanitizer-synccheck -lid0'} }

  # Thrust:
  test_cpu: { name: 'TestCPU', gpu: false, needs: 'build', invoke: { prefix: 'test', args: '-cpu-only'} }
  test_gpu: { name: 'TestGPU', gpu: true,  needs: 'build', invoke: { prefix: 'test', args: '-gpu-only'} }

  # Python:
  test_cuda_cccl:        { name: "cuda.cccl",        gpu: true }
  test_cuda_cooperative: { name: "cuda.cooperative", gpu: true }
  test_cuda_parallel:    { name: "cuda.parallel",    gpu: true }

# Projects have the following properties:
#
# Keys are project subdirectories names. These will also be used in script names.
#
# - stds: A list of C++ standards to test. Required.
# - name: The human-readable name of the project. Default is the project key.
# - job_map: Map general jobs to arrays of project-specific jobs.
#            Useful for things like splitting cpu/gpu testing for a project.
#            E.g. "job_map: { test: ['test_cpu', 'test_gpu'] }" replaces
#            the "test" job with distinct "test_cpu" and "test_gpu" jobs.
projects:
  cccl:
    name: 'CCCL'
    stds: [17, 20]
  libcudacxx:
    name: 'libcu++'
    stds: [17, 20]
  cub:
    name: 'CUB'
    stds: [17, 20]
    job_map:
      test: ['test_nolid', 'test_lid0', 'test_lid1', 'test_lid2']
      compute_sanitizer:
        - compute_mem_nolid
        - compute_mem_lid0
        - compute_race_nolid
        - compute_race_lid0
        - compute_init_nolid
        - compute_init_lid0
        - compute_sync_nolid
        - compute_sync_lid0
  thrust:
    name: 'Thrust'
    stds: [17, 20]
    job_map: { test: ['test_cpu', 'test_gpu'] }
  cudax:
    stds: [17, 20]
  stdpar:
    name: 'NVHPC stdpar'
    stds: [17, 20]
  python:
    name: "Python"
    job_map: { build: [], test: ['test_cuda_cccl', 'test_cuda_cooperative', 'test_cuda_parallel'] }
  cccl_c_parallel:
    name: 'CCCL C Parallel'
    stds: [20]

# testing -> Runner with GPU is in a nv-gh-runners testing pool
gpus:
  v100:     { sm: 70 } # 32 GB,  40 runners
  t4:       { sm: 75 } # 16 GB,  10 runners
  rtx2080:  { sm: 75 } #  8 GB,  12 runners
  rtxa6000: { sm: 86 } # 48 GB,  12 runners
  l4:       { sm: 89 } # 24 GB,  48 runners
  rtx4090:  { sm: 89 } # 24 GB,  10 runners
  h100:     { sm: 90 } # 80 GB,  16 runners

# Tags are used to define a `matrix job` in the workflow section.
#
# Tags have the following options:
#  - required: Whether the tag is required. Default is false.
#  - default: The default value for the tag. Default is null.
tags:
   # An array of jobs (e.g. 'build', 'test', 'nvrtc', 'infra', 'verify_codegen', ...)
   # See the `jobs` map.
  jobs: { required: true }
  # CUDA ToolKit version
  # See the `ctks` map.
  ctk: { default: 'curr' }
  # CPU architecture
  cpu: { default: 'amd64' }
  # GPU model
  gpu: { default: 'rtx2080' }
  # Host compiler {name, version, exe}
  # See the `host_compilers` map.
  cxx: { default: 'gcc' }
  # Device compiler.
  # See the `device_compilers` map.
  cudacxx: { default: 'nvcc' }
  # Project name (e.g. libcudacxx, cub, thrust, cccl)
  # See the `projects` map.
  project: { default: ['libcudacxx', 'cub', 'thrust'] }
  # C++ standard
  # If set to 'all', all stds supported by the ctk/compilers/project are used.
  # If set to 'min', 'max', or 'minmax', the minimum, maximum, or both stds are used.
  # If set, will be passed to script with `-std <std>`.
  std: { required: false }
  # GPU architecture
  # - If set, passed to script with `-arch <sm>`.
  # - Format is the same as `CMAKE_CUDA_ARCHITECTURES`:
  #   - PTX only: 70-virtual
  #   - SASS only: 70-real
  #   - Both: 70
  # - Can pass multiple architectures via "60;70-real;80-virtual"
  # - Defaults to use the settings in the CMakePresets.json file.
  # - Will be exploded if an array, e.g. `sm: ['60;70;80;90', '90a']` creates two jobs.
  # - Set to 'gpu' to only target the GPU in the `gpu` tag.
  sm: { required: false }
  # Additional CMake options to pass to the build.
  # If set, passed to script with `-cmake_options "<cmake_options>"`.
  cmake_options: { required: false }
